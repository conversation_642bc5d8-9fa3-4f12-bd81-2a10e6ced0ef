import typing
import discord
import logging
import wavelink
import aiohttp
import io
from discord.ext import commands, tasks

try:
    from PIL import Image, ImageDraw, ImageFont, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from utilities import checks
from utilities import converters
from utilities import decorators

from utilities import spotify

logger = logging.getLogger(__name__)


class SpotifyPaginationView(discord.ui.View):
    """Simple pagination view for Spotify results using buttons"""

    def __init__(self, ctx, entries, per_page=10):
        super().__init__(timeout=300)  # 5 minute timeout
        self.ctx = ctx
        self.entries = entries
        self.per_page = per_page
        self.current_page = 0
        self.max_pages = (len(entries) - 1) // per_page + 1

        # Create embed
        self.embed = discord.Embed(color=ctx.bot.mode.EMBED_COLOR)

        # Update button states
        self.update_buttons()

    def update_buttons(self):
        """Update button states based on current page"""
        self.first_page.disabled = self.current_page == 0
        self.prev_page.disabled = self.current_page == 0
        self.next_page.disabled = self.current_page >= self.max_pages - 1
        self.last_page.disabled = self.current_page >= self.max_pages - 1

    def get_page_content(self):
        """Get the content for the current page"""
        start = self.current_page * self.per_page
        end = start + self.per_page
        page_entries = self.entries[start:end]

        # Format entries with numbers
        formatted_entries = []
        for i, entry in enumerate(page_entries, start=start + 1):
            formatted_entries.append(f"{i}. {entry}")

        return "\n".join(formatted_entries)

    def update_embed(self):
        """Update the embed with current page content"""
        self.embed.description = self.get_page_content()
        if self.max_pages > 1:
            self.embed.set_footer(text=f"Page {self.current_page + 1}/{self.max_pages} ({len(self.entries)} entries)")
        else:
            self.embed.set_footer(text=f"{len(self.entries)} entries")

    async def send_initial_message(self):
        """Send the initial message with pagination"""
        self.update_embed()
        return await self.ctx.send(embed=self.embed, view=self)

    @discord.ui.button(label="<<", style=discord.ButtonStyle.gray)
    async def first_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = 0
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label="<", style=discord.ButtonStyle.gray)
    async def prev_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = max(0, self.current_page - 1)
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label=">", style=discord.ButtonStyle.gray)
    async def next_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = min(self.max_pages - 1, self.current_page + 1)
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label=">>", style=discord.ButtonStyle.gray)
    async def last_page(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        self.current_page = self.max_pages - 1
        self.update_buttons()
        self.update_embed()
        await interaction.response.edit_message(embed=self.embed, view=self)

    @discord.ui.button(label="🗑", style=discord.ButtonStyle.red)
    async def delete_message(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user != self.ctx.author:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return

        await interaction.response.defer()
        await interaction.delete_original_response()
        self.stop()

    async def on_timeout(self):
        """Called when the view times out"""
        try:
            # Disable all buttons
            for item in self.children:
                item.disabled = True
            await self.message.edit(view=self)
        except:
            pass


async def setup(bot):
    await bot.add_cog(Spotify(bot))


class Spotify(commands.Cog):
    """
    Spotify account statistics.
    """

    def __init__(self, bot):
        self.bot = bot
        self.time_map = {
            "short_term": "month",
            "medium_term": "semester",
            "long_term": "year",
        }
        if self.bot.production:
            self.live_stats.start()

    @tasks.loop(seconds=1)
    async def live_stats(self):

        r = await self.bot.http_utils.post(
            "http://localhost:3000/stats",
            data={
                "members": len(self.bot.users),
                "servers": len(self.bot.guilds),
                "messages": sum(self.bot.message_stats.values()),
            },
            headers={"Content-Type": "application/json"},
            res_method="json",
        )

    def truncate(self, string, max_chars=20):
        return (string[: max_chars - 3] + "...") if len(string) > max_chars else string

    def hyperlink(self, name, url, max_chars=20):
        return f"**[{self.truncate(name, max_chars)}]({url})**"

    def format_track(self, track):
        return self.hyperlink(track["name"], track["external_urls"]["spotify"])

    def format_artists(self, artists):
        artists = artists[:3]  # more than 3 artists looks bad on embed
        max_chars = 40 // len(artists)
        return ", ".join(
            self.hyperlink(
                artist["name"], artist["external_urls"]["spotify"], max_chars
            )
            for artist in artists
        )

    async def get_spotify_user(self, ctx, user):
        try:
            sp_user = await spotify.User.load(user.id)
            if not sp_user:
                if user == ctx.author:
                    # Generate state parameter with user ID
                    state = str(user.id)
                    auth_url = spotify.oauth.get_auth_url(state)

                    view = discord.ui.View()
                    button = discord.ui.Button(
                        label="Click here to connect your Spotify account!",
                        url=auth_url,
                    )
                    view.add_item(button)
                    await ctx.fail(
                        "You have not connected your Spotify account yet.", view=view
                    )
                else:
                    await ctx.fail(
                        f"User **{user}** `{user.id}` has not connected their Spotify account yet."
                    )
            return sp_user
        except Exception as e:
            logger.error(f"Error loading Spotify user {user.id}: {e}")
            await ctx.fail("An error occurred while loading your Spotify account. Please try again later.")
            return None

    @decorators.group(
        name="spotify",
        aliases=["sp", "sf"],
        brief="Manage spotify stats and playlists",
    )
    @checks.cooldown()
    async def _sp(self, ctx):
        if not ctx.invoked_subcommand:
            sp_user = await self.get_spotify_user(ctx, ctx.author)
            if not sp_user:
                # Generate a state parameter with the user's ID to track who's authenticating
                state = str(ctx.author.id)
                auth_url = spotify.oauth.get_auth_url(state)  # Use spotify.oauth instead of discord_oauth
                
                view = discord.ui.View()
                button = discord.ui.Button(
                    label="Click here to connect your Spotify account!",
                    url=auth_url
                )
                view.add_item(button)
                await ctx.send(
                    "You need to connect your Spotify account to use this command.",
                    view=view
                )
            else:
                await ctx.success("You have already connected your spotify account.")

    @_sp.command(brief="Get top spotify tracks.", aliases=["tt"])
    async def top_tracks(
        self,
        ctx,
        user: typing.Optional[converters.DiscordMember],
        time_frame: converters.SpotifyTimeFrame = "short_term",
    ):
        user = user or ctx.author
        sp_user = await self.get_spotify_user(ctx, user)
        if not sp_user:
            return

        try:
            top_tracks = await sp_user.get_top_tracks(time_range=time_frame)
            logger.debug(f"Top tracks response for user {user.id}: {top_tracks}")

            if not top_tracks.get("items"):
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` has' if user != ctx.author else 'You have'} no top tracks."
                )
                return

            entries = [
                f"{self.format_track(track)} by {self.format_artists(track['artists'])}"
                for track in top_tracks["items"]
            ]

            # Create button-based pagination view
            view = SpotifyPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{user.display_name}'s top Spotify tracks in the past {self.time_map[time_frame]}."
            view.embed.set_thumbnail(url=spotify.CONSTANTS.WHITE_ICON)
            view.message = await view.send_initial_message()
        except Exception as e:
            logger.error(f"Error getting top tracks for user {user.id}: {e}")
            await ctx.fail("An error occurred while fetching your top tracks. Please try again later.")

    @_sp.command(brief="Get top spotify artists.", aliases=["ta"])
    async def top_artists(
        self,
        ctx,
        user: typing.Optional[converters.DiscordMember],
        time_frame: converters.SpotifyTimeFrame = "short_term",
    ):
        user = user or ctx.author
        sp_user = await self.get_spotify_user(ctx, user)
        if not sp_user:
            return

        try:
            top_artists = await sp_user.get_top_artists(time_range=time_frame)
            logger.debug(f"Top artists response for user {user.id}: {top_artists}")

            if not top_artists.get("items"):
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` has' if user != ctx.author else 'You have'} no top artists."
                )
                return

            entries = [
                self.hyperlink(
                    artist["name"], artist["external_urls"]["spotify"], max_chars=50
                )
                for artist in top_artists["items"]
            ]

            # Create button-based pagination view
            view = SpotifyPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{user.display_name}'s top Spotify artists in the past {self.time_map[time_frame]}."
            view.embed.set_thumbnail(url=spotify.CONSTANTS.WHITE_ICON)
            view.message = await view.send_initial_message()
        except Exception as e:
            logger.error(f"Error getting top artists for user {user.id}: {e}")
            await ctx.fail("An error occurred while fetching your top artists. Please try again later.")

    @_sp.command(brief="Get recent Spotify listens", aliases=["r"])
    async def recent(self, ctx, *, user: converters.DiscordMember = None):
        user = user or ctx.author
        sp_user = await self.get_spotify_user(ctx, user)
        if not sp_user:
            return

        try:
            recent = await sp_user.get_recently_played()
            logger.debug(f"Recent tracks response for user {user.id}: {recent}")

            if not recent.get("items"):
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` has' if user != ctx.author else 'You have'} no recent tracks."
                )
                return

            entries = [
                f"{self.format_track(item['track'])} by {self.format_artists(item['track']['artists'])}"
                for item in recent["items"]
            ]

            # Create button-based pagination view
            view = SpotifyPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{user.display_name}'s recently played Spotify tracks."
            view.embed.set_thumbnail(url=spotify.CONSTANTS.WHITE_ICON)
            view.message = await view.send_initial_message()
        except Exception as e:
            logger.error(f"Error getting recent tracks for user {user.id}: {e}")
            await ctx.fail("An error occurred while fetching your recent tracks. Please try again later.")

    @_sp.command(brief="Get current song data")
    async def plst(self, ctx, *, user: converters.DiscordMember = None):
        sp_user = await self.get_spotify_user(ctx, ctx.author)
        url = "https://open.spotify.com/playlist/490Su62TmufWRkdxmggDnY?si=37542cff68fb4899"
        url = "https://open.spotify.com/playlist/6K9uHsMwuRGRWvHLNK4rT2?si=f7be2003e315494b"
        uri = spotify.url_to_uri(url)
        data = await spotify.get_playlist(uri)
        del data["tracks"]
        print(data)

    @_sp.command(brief="Get user playlists")
    async def playlists(self, ctx, *, user: converters.DiscordMember = None):
        user = user or ctx.author
        sp_user = await self.get_spotify_user(ctx, ctx.author)
        if not sp_user:
            return

        try:
            data = await sp_user.get_playlists()
            if not data.get("items"):
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` has' if user != ctx.author else 'You have'} no playlists."
                )
                return

            entries = [
                f"{self.format_track(playlist)} ID: `{playlist['id']}`"
                for playlist in data["items"]
            ]

            # Create button-based pagination view
            view = SpotifyPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{user.display_name}'s Spotify playlists."
            view.embed.set_thumbnail(url=spotify.CONSTANTS.WHITE_ICON)
            view.message = await view.send_initial_message()
        except Exception as e:
            logger.error(f"Error getting playlists for user {user.id}: {e}")
            await ctx.fail("An error occurred while fetching your playlists. Please try again later.")

    @_sp.command(brief="Get user playlists")
    async def user_playlists(self, ctx, username):
        try:
            data = await spotify.get_user_playlists(username)
            if not data.get("items"):
                await ctx.fail(f"No user found with username: `{username}`")
                return

            entries = [
                f"{self.format_track(playlist)} ID: `{playlist['id']}`"
                for playlist in data["items"]
            ]

            # Create button-based pagination view
            view = SpotifyPaginationView(ctx, entries, per_page=10)
            view.embed.title = f"{username}'s Spotify playlists."
            view.embed.set_thumbnail(url=spotify.CONSTANTS.WHITE_ICON)
            view.message = await view.send_initial_message()
        except Exception as e:
            logger.error(f"Error getting playlists for username {username}: {e}")
            await ctx.fail("An error occurred while fetching the user's playlists. Please try again later.")

    @_sp.command(brief="Show current Spotify playback", aliases=["np", "nowplaying"])
    async def now_playing(self, ctx, *, user: converters.DiscordMember = None):
        user = user or ctx.author
        sp_user = await self.get_spotify_user(ctx, user)
        if not sp_user:
            return

        try:
            current = await sp_user.get_currently_playing()
            logger.debug(f"Currently playing response for user {user.id}: {current}")

            if not current:
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` is' if user != ctx.author else 'You are'} not currently playing anything on Spotify. (No response from API)"
                )
                return

            if not current.get("item"):
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` is' if user != ctx.author else 'You are'} not currently playing anything on Spotify. (No track item in response)"
                )
                return

            track = current["item"]
            is_playing = current.get("is_playing", False)
            progress_ms = current.get("progress_ms", 0)
            device = current.get("device", {})

            # Create embed
            embed = discord.Embed(
                title=f"<a:MusicNotes:1393302817053081690> {'Now Playing' if is_playing else 'Paused'} on Spotify",
                color=0x2596BE if is_playing else 0x808080
            )

            # Track info
            embed.add_field(
                name="Track",
                value=f"**[{track['name']}]({track['external_urls']['spotify']})**",
                inline=False
            )

            # Artists
            artists = ", ".join([artist["name"] for artist in track["artists"]])
            embed.add_field(name="Artist(s)", value=artists, inline=True)

            # Album
            if track.get("album"):
                embed.add_field(
                    name="Album",
                    value=f"**[{track['album']['name']}]({track['album']['external_urls']['spotify']})**",
                    inline=True
                )

            # Progress
            duration_ms = track.get("duration_ms", 0)
            if duration_ms > 0:
                progress_str = f"{progress_ms // 60000}:{(progress_ms // 1000) % 60:02d}"
                duration_str = f"{duration_ms // 60000}:{(duration_ms // 1000) % 60:02d}"
                embed.add_field(name="Progress", value=f"{progress_str} / {duration_str}", inline=True)

            # Device info
            if device:
                device_name = device.get("name", "Unknown Device")
                device_type = device.get("type", "").title()
                embed.add_field(name="Device", value=f"{device_name} ({device_type})", inline=True)

            # Volume
            if device.get("volume_percent") is not None:
                embed.add_field(name="Volume", value=f"{device['volume_percent']}%", inline=True)

            # Thumbnail
            if track.get("album", {}).get("images"):
                embed.set_thumbnail(url=track["album"]["images"][0]["url"])

            embed.set_footer(
                text=f"Spotify • {user.display_name}",
                icon_url=spotify.CONSTANTS.WHITE_ICON
            )

            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error getting now playing for user {user.id}: {e}")
            await ctx.fail("An error occurred while fetching your current playback. Please try again later.")

    @_sp.command(brief="Show current Spotify playback with canvas", aliases=["canvas", "card"])
    async def now_playing_canvas(self, ctx, *, user: converters.DiscordMember = None):
        """Show current Spotify playback with a beautiful canvas design"""
        if not PIL_AVAILABLE:
            await ctx.fail("Canvas feature requires PIL (Pillow) to be installed. Use `!spotify nowplaying` for the regular version.")
            return

        user = user or ctx.author
        sp_user = await self.get_spotify_user(ctx, user)
        if not sp_user:
            return

        try:
            current = await sp_user.get_currently_playing()

            if not current or not current.get("item"):
                await ctx.fail(
                    f"{f'User **{user}** `{user.id}` is' if user != ctx.author else 'You are'} not currently playing anything on Spotify."
                )
                return

            track = current["item"]
            is_playing = current.get("is_playing", False)
            progress_ms = current.get("progress_ms", 0)
            duration_ms = track.get("duration_ms", 0)

            # Create canvas
            canvas_buffer = await self.create_now_playing_canvas(track, progress_ms, duration_ms, is_playing)

            # Send as file
            file = discord.File(canvas_buffer, filename="now_playing.png")

            embed = discord.Embed(
                title=f"<a:MusicNotes:1393302817053081690> {'Now Playing' if is_playing else 'Paused'} on Spotify",
                color=0x2596BE if is_playing else 0x808080
            )
            embed.set_footer(text=f"Spotify • {user.display_name}")
            embed.set_image(url="attachment://now_playing.png")

            await ctx.send(embed=embed, file=file)

        except Exception as e:
            logger.error(f"Error creating canvas for user {user.id}: {e}")
            await ctx.fail("An error occurred while creating the now playing canvas.")

    async def create_now_playing_canvas(self, track, progress_ms, duration_ms, is_playing):
        width, height = 1280, 400  # Match screenshot ratio

        # Load album art
        album_image = None
        if track.get("album", {}).get("images"):
            album_url = track["album"]["images"][0]["url"]
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(album_url) as resp:
                        if resp.status == 200:
                            image_data = await resp.read()
                            album_image = Image.open(io.BytesIO(image_data))
            except Exception as e:
                logger.error(f"Error fetching album image: {e}")

        # Base canvas with black background
        canvas = Image.new('RGB', (width, height), (0, 0, 0))
        draw = ImageDraw.Draw(canvas)

        # Blurred background from album
        if album_image:
            bg_image = album_image.resize((width, height), Image.Resampling.LANCZOS).convert("RGBA")
            bg_image = bg_image.filter(ImageFilter.GaussianBlur(radius=15))  # Reduced from 25 to 15
            overlay = Image.new('RGBA', (width, height), (0, 0, 0, 160))
            bg_image = Image.alpha_composite(bg_image, overlay)
            canvas.paste(bg_image.convert("RGB"))

        # Fonts - made smaller
        try:
            title_font = ImageFont.truetype("arialbd.ttf", 45)  # Reduced from 60 to 45
            artist_font = ImageFont.truetype("arialbd.ttf", 24)  # Reduced from 32 to 24
            album_font = ImageFont.truetype("arial.ttf", 22)    # Reduced from 30 to 22
            time_font = ImageFont.truetype("arial.ttf", 20)     # Reduced from 24 to 20
        except:
            title_font = ImageFont.load_default()
            artist_font = ImageFont.load_default()
            album_font = ImageFont.load_default()
            time_font = ImageFont.load_default()

        # Extract text
        track_name = track["name"][:60]
        artists = ", ".join([a["name"] for a in track["artists"]])[:45]
        album_name = track.get("album", {}).get("name", "")[:45]

        # Spotify logo circle
        circle_radius = 40
        logo_x = width // 2 - circle_radius
        logo_y = 30
        # Spotify logo from local assets
        try:
            logo_path = "data/assets/spotify.png"
            logo_image = Image.open(logo_path).convert("RGBA")
            logo_size = circle_radius * 2
            logo_image = logo_image.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
            canvas.paste(logo_image, (logo_x, logo_y), logo_image)
        except Exception as e:
            logger.error(f"Error loading Spotify logo from assets: {e}")
            # Fallback to green circle if logo fails
            draw.ellipse([logo_x, logo_y, logo_x + circle_radius * 2, logo_y + circle_radius * 2], fill=(29, 185, 84))

        # Text positioning
        y_start = logo_y + circle_radius * 2 + 30
        spacing = 50

        # Artist
        try:
            artist_w, _ = draw.textsize(artists, font=artist_font)
        except AttributeError:
            artist_bbox = draw.textbbox((0, 0), artists, font=artist_font)
            artist_w = artist_bbox[2] - artist_bbox[0]
        draw.text(((width - artist_w) // 2, y_start), artists, font=artist_font, fill=(180, 200, 255))
        y_start += spacing

        # Title
        try:
            title_w, _ = draw.textsize(track_name, font=title_font)
        except AttributeError:
            title_bbox = draw.textbbox((0, 0), track_name, font=title_font)
            title_w = title_bbox[2] - title_bbox[0]
        draw.text(((width - title_w) // 2, y_start), track_name, font=title_font, fill=(255, 255, 255))
        y_start += spacing

        # Album
        try:
            album_w, _ = draw.textsize(album_name, font=album_font)
        except AttributeError:
            album_bbox = draw.textbbox((0, 0), album_name, font=album_font)
            album_w = album_bbox[2] - album_bbox[0]
        draw.text(((width - album_w) // 2, y_start), album_name, font=album_font, fill=(160, 180, 220))

        # Progress bar
        bar_y = height - 90
        bar_x = 150
        bar_width = width - 300
        bar_height = 6
        radius = 6

        # Draw base bar
        try:
            draw.rounded_rectangle([bar_x, bar_y, bar_x + bar_width, bar_y + bar_height], radius=radius, fill=(80, 80, 80))
        except AttributeError:
            draw.rectangle([bar_x, bar_y, bar_x + bar_width, bar_y + bar_height], fill=(80, 80, 80))

        # Draw progress
        if duration_ms > 0:
            progress_ratio = min(progress_ms / duration_ms, 1.0)
            fill_width = int(bar_width * progress_ratio)
            try:
                draw.rounded_rectangle([bar_x, bar_y, bar_x + fill_width, bar_y + bar_height], radius=radius, fill=(255, 255, 255))
            except AttributeError:
                draw.rectangle([bar_x, bar_y, bar_x + fill_width, bar_y + bar_height], fill=(255, 255, 255))

            # Progress dot
            dot_x = bar_x + fill_width
            dot_y = bar_y + bar_height // 2
            draw.ellipse([dot_x - 8, dot_y - 8, dot_x + 8, dot_y + 8], fill=(255, 255, 255))

        # Timestamps
        current_time = f"{progress_ms // 60000}:{(progress_ms // 1000) % 60:02d}"
        total_time = f"{duration_ms // 60000}:{(duration_ms // 1000) % 60:02d}"

        draw.text((bar_x, bar_y + 15), current_time, font=time_font, fill=(200, 200, 200))
        try:
            total_w, _ = draw.textsize(total_time, font=time_font)
        except AttributeError:
            total_bbox = draw.textbbox((0, 0), total_time, font=time_font)
            total_w = total_bbox[2] - total_bbox[0]
        draw.text((bar_x + bar_width - total_w, bar_y + 15), total_time, font=time_font, fill=(200, 200, 200))

        # Final buffer
        buffer = io.BytesIO()
        canvas.save(buffer, format='PNG', quality=95)
        buffer.seek(0)
        return buffer

    @_sp.command(brief="Check your Spotify connection and scopes")
    async def debug(self, ctx):
        """Debug command to check Spotify connection and scopes"""
        sp_user = await self.get_spotify_user(ctx, ctx.author)
        if not sp_user:
            return

        try:
            # Try to get profile first
            profile = await sp_user.get_profile()

            embed = discord.Embed(
                title="🔍 Spotify Connection Debug",
                color=0x2596BE
            )

            embed.add_field(
                name="✅ Profile Access",
                value=f"Connected as: **{profile.get('display_name', 'Unknown')}**",
                inline=False
            )

            # Test different endpoints
            tests = []

            # Test currently playing
            try:
                current = await sp_user.get_currently_playing()
                if current is None:
                    tests.append("❌ Currently Playing: No response (may need scope)")
                elif current.get("item"):
                    tests.append("✅ Currently Playing: Working")
                else:
                    tests.append("⚠️ Currently Playing: No active playback")
            except Exception as e:
                tests.append(f"❌ Currently Playing: Error - {str(e)[:50]}")

            # Test playback state
            try:
                playback = await sp_user.get_playback_state()
                if playback is None:
                    tests.append("❌ Playback State: No response (may need scope)")
                else:
                    tests.append("✅ Playback State: Working")
            except Exception as e:
                tests.append(f"❌ Playback State: Error - {str(e)[:50]}")

            # Test playlists
            try:
                playlists = await sp_user.get_playlists(limit=1)
                if playlists.get("items"):
                    tests.append("✅ Playlists: Working")
                else:
                    tests.append("⚠️ Playlists: No playlists found")
            except Exception as e:
                tests.append(f"❌ Playlists: Error - {str(e)[:50]}")

            embed.add_field(
                name="🧪 API Tests",
                value="\n".join(tests),
                inline=False
            )

            embed.add_field(
                name="💡 Troubleshooting",
                value="If you see scope errors, run `!spotify disconnect` then `!spotify` to reconnect with new permissions.",
                inline=False
            )

            await ctx.send(embed=embed)

        except Exception as e:
            logger.error(f"Error in debug command: {e}")
            await ctx.fail(f"Debug failed: {str(e)}")

    @_sp.command(brief="Play a Spotify playlist or track", aliases=["play"])
    async def spotify_play(self, ctx, *, spotify_url: str):
        """Play a Spotify playlist, album, or track in voice channel"""
        # Check if user is in voice channel
        if not ctx.author.voice:
            await ctx.fail("You need to be in a voice channel to use this command.")
            return

        # Check if bot has music cog
        music_cog = self.bot.get_cog("Music")
        if not music_cog:
            await ctx.fail("Music functionality is not available.")
            return

        try:
            # Convert URL to URI if needed
            if "open.spotify.com" in spotify_url:
                uri = spotify.url_to_uri(spotify_url)
            else:
                uri = spotify_url

            if not uri or not uri.startswith("spotify:"):
                await ctx.fail("Please provide a valid Spotify URL or URI.")
                return

            # Determine type and extract ID
            uri_parts = uri.split(":")
            if len(uri_parts) < 3:
                await ctx.fail("Invalid Spotify URI format.")
                return

            spotify_type = uri_parts[1]  # track, playlist, album
            spotify_id = uri_parts[2]

            if spotify_type == "track":
                await self._play_spotify_track(ctx, uri, music_cog)
            elif spotify_type == "playlist":
                await self._play_spotify_playlist(ctx, uri, music_cog)
            elif spotify_type == "album":
                await self._play_spotify_album(ctx, uri, music_cog)
            else:
                await ctx.fail(f"Unsupported Spotify type: {spotify_type}")

        except Exception as e:
            logger.error(f"Error playing Spotify content: {e}")
            await ctx.fail("An error occurred while processing the Spotify content.")

    async def _play_spotify_track(self, ctx, uri, music_cog):
        """Play a single Spotify track"""
        try:
            track_data = await spotify.get_track(uri)

            # Create search query
            artists = ", ".join([artist["name"] for artist in track_data["artists"]])
            search_query = f"{track_data['name']} {artists}"

            # Use music cog to play
            await music_cog.play(ctx, query=search_query)

        except Exception as e:
            logger.error(f"Error playing Spotify track: {e}")
            await ctx.fail("Could not play this Spotify track.")

    async def _play_spotify_playlist(self, ctx, uri, music_cog):
        """Play a Spotify playlist"""
        try:
            # Get playlist info
            playlist_data = await spotify.get_playlist(uri)
            playlist_name = playlist_data.get("name", "Unknown Playlist")

            # Get all tracks from playlist
            all_tracks = []
            offset = 0
            limit = 50

            while True:
                tracks_data = await spotify.get_playlist_tracks(uri, limit=limit, offset=offset)
                tracks = tracks_data.get("items", [])

                if not tracks:
                    break

                all_tracks.extend(tracks)

                if len(tracks) < limit:
                    break

                offset += limit

            if not all_tracks:
                await ctx.fail("This playlist appears to be empty.")
                return

            # Connect to voice channel
            voice_channel = ctx.author.voice.channel
            vc = ctx.voice_client or await voice_channel.connect(cls=wavelink.Player)
            vc.ctx = ctx

            # Add tracks to queue
            added_count = 0
            failed_count = 0

            await ctx.send(f"<a:MusicNotes:1393302817053081690> Adding **{len(all_tracks)}** tracks from **{playlist_name}** to the queue...")

            for item in all_tracks:
                try:
                    track = item.get("track")
                    if not track or track.get("is_local"):
                        continue

                    # Create search query
                    artists = ", ".join([artist["name"] for artist in track["artists"]])
                    search_query = f"{track['name']} {artists}"

                    # Search for track
                    import wavelink
                    search_results = await wavelink.Playable.search(search_query)

                    if search_results:
                        await vc.queue.put_wait(search_results[0])
                        added_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"Error adding track to queue: {e}")
                    failed_count += 1

            # Start playing if not already playing
            if not vc.playing and not vc.queue.is_empty:
                track = await vc.queue.get_wait()
                await vc.play(track)
                await music_cog.display_player_embed(vc, track, ctx)

            # Send summary
            summary = f"✅ Added **{added_count}** tracks from **{playlist_name}** to the queue."
            if failed_count > 0:
                summary += f"\n⚠️ Could not find **{failed_count}** tracks."

            await ctx.send(summary)

        except Exception as e:
            logger.error(f"Error playing Spotify playlist: {e}")
            await ctx.fail("Could not play this Spotify playlist.")

    async def _play_spotify_album(self, ctx, uri, music_cog):
        """Play a Spotify album"""
        try:
            # Get album info
            album_data = await spotify.get_playlist(uri)  # get_playlist works for albums too
            album_name = album_data.get("name", "Unknown Album")

            # Get all tracks from album
            all_tracks = []
            offset = 0
            limit = 50

            while True:
                tracks_data = await spotify.get_album_tracks(uri, limit=limit, offset=offset)
                tracks = tracks_data.get("items", [])

                if not tracks:
                    break

                all_tracks.extend(tracks)

                if len(tracks) < limit:
                    break

                offset += limit

            if not all_tracks:
                await ctx.fail("This album appears to be empty.")
                return

            # Connect to voice channel
            voice_channel = ctx.author.voice.channel
            vc = ctx.voice_client or await voice_channel.connect(cls=wavelink.Player)
            vc.ctx = ctx

            # Add tracks to queue
            added_count = 0
            failed_count = 0

            await ctx.send(f"<a:MusicNotes:1393302817053081690> Adding **{len(all_tracks)}** tracks from **{album_name}** to the queue...")

            for track in all_tracks:
                try:
                    # Create search query
                    artists = ", ".join([artist["name"] for artist in track["artists"]])
                    search_query = f"{track['name']} {artists}"

                    # Search for track
                    import wavelink
                    search_results = await wavelink.Playable.search(search_query)

                    if search_results:
                        await vc.queue.put_wait(search_results[0])
                        added_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"Error adding track to queue: {e}")
                    failed_count += 1

            # Start playing if not already playing
            if not vc.playing and not vc.queue.is_empty:
                track = await vc.queue.get_wait()
                await vc.play(track)
                await music_cog.display_player_embed(vc, track, ctx)

            # Send summary
            summary = f"✅ Added **{added_count}** tracks from **{album_name}** to the queue."
            if failed_count > 0:
                summary += f"\n⚠️ Could not find **{failed_count}** tracks."

            await ctx.send(summary)

        except Exception as e:
            logger.error(f"Error playing Spotify album: {e}")
            await ctx.fail("Could not play this Spotify album.")

    @_sp.command(brief="Play your current Spotify track in voice channel", aliases=["vc"])
    async def voice_channel(self, ctx):
        """Play your current Spotify track in a voice channel"""
        # Check if user is in voice channel
        if not ctx.author.voice:
            await ctx.fail("You need to be in a voice channel to use this command.")
            return

        # Check if bot has music cog
        music_cog = self.bot.get_cog("Music")
        if not music_cog:
            await ctx.fail("Music functionality is not available.")
            return

        # Get Spotify user
        sp_user = await self.get_spotify_user(ctx, ctx.author)
        if not sp_user:
            return

        try:
            # Get currently playing track
            current = await sp_user.get_currently_playing()
            logger.debug(f"Currently playing response for user {ctx.author.id}: {current}")

            if not current:
                await ctx.fail("You are not currently playing anything on Spotify.")
                return

            if not current.get("item"):
                await ctx.fail("No track information available from Spotify.")
                return

            track = current["item"]

            # Check if it's a local file (can't be played)
            if track.get("is_local"):
                await ctx.fail("Cannot play local files from Spotify. Please play a track from Spotify's catalog.")
                return

            # Create search query from Spotify track
            artists = ", ".join([artist["name"] for artist in track["artists"]])
            search_query = f"{track['name']} {artists}"

            # Connect to voice channel
            voice_channel = ctx.author.voice.channel
            perms = voice_channel.permissions_for(ctx.guild.me)

            if not perms.connect or not perms.speak:
                await ctx.fail("I need connect & speak permissions in your voice channel.")
                return

            vc = ctx.voice_client or await voice_channel.connect(cls=wavelink.Player)
            vc.ctx = ctx

            # Search for the track
            search_results = await wavelink.Playable.search(search_query)

            if not search_results:
                await ctx.fail(f"Could not find **{track['name']}** by **{artists}** on YouTube.")
                return

            # Get the best match
            selected_track = search_results[0]

            # Play or queue the track
            if not vc.playing:
                await vc.play(selected_track)

                # Create embed for now playing
                embed = discord.Embed(
                    title="<a:MusicNotes:1393302817053081690> Now Playing from Spotify",
                    description=f"**[{selected_track.title}]({selected_track.uri})**",
                    color=0x2596BE
                )
                embed.add_field(name="Original Spotify Track", value=f"**[{track['name']}]({track['external_urls']['spotify']})**", inline=False)
                embed.add_field(name="Artist(s)", value=artists, inline=True)

                duration = f"{selected_track.length // 60000}:{(selected_track.length // 1000) % 60:02d}"
                embed.add_field(name="Duration", value=duration, inline=True)
                embed.add_field(name="Source", value="YouTube", inline=True)

                if track.get("album", {}).get("images"):
                    embed.set_thumbnail(url=track["album"]["images"][0]["url"])

                embed.set_footer(
                    text=f"Requested by {ctx.author.display_name}",
                    icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url
                )

                await ctx.send(embed=embed)

                # Display the music player embed
                await music_cog.display_player_embed(vc, selected_track, ctx)
            else:
                await vc.queue.put_wait(selected_track)
                await ctx.send(embed=discord.Embed(
                    description=f"✅ Added **{track['name']}** by **{artists}** to the queue.",
                    color=0x00FF00
                ))

        except Exception as e:
            logger.error(f"Error playing current Spotify track for user {ctx.author.id}: {e}")
            await ctx.fail("An error occurred while trying to play your current Spotify track. Please try again later.")

    @_sp.command(brief="Disconnect your Spotify account.")
    async def disconnect(self, ctx):
        query = """
                DELETE FROM spotify_auth
                WHERE user_id = $1;
                """
        status = await self.bot.cxn.execute(query, ctx.author.id)
        if status != "DELETE 0":
            await ctx.success("Successfully disconnected your spotify account.")
        else:
            await ctx.fail("You have not connected your Spotify account yet.")

    @_sp.command(brief="Show your playlists with play options", aliases=["myplaylists"])
    async def my_playlists(self, ctx):
        """Show user's Spotify playlists with buttons to play them"""
        sp_user = await self.get_spotify_user(ctx, ctx.author)
        if not sp_user:
            return

        try:
            data = await sp_user.get_playlists()
            if not data.get("items"):
                await ctx.fail("You have no playlists.")
                return

            # Create view with playlist buttons
            view = SpotifyPlaylistView(ctx, data["items"][:10])  # Limit to 10 playlists
            embed = discord.Embed(
                title=f"{ctx.author.display_name}'s Spotify Playlists",
                description="Click a button below to play a playlist in voice channel!",
                color=0x2596BE
            )
            embed.set_thumbnail(url=spotify.CONSTANTS.WHITE_ICON)

            # Add playlist info to embed
            playlist_info = []
            for i, playlist in enumerate(data["items"][:10], 1):
                track_count = playlist.get("tracks", {}).get("total", 0)
                playlist_info.append(f"{i}. **{playlist['name']}** ({track_count} tracks)")

            embed.add_field(name="Available Playlists", value="\n".join(playlist_info), inline=False)

            await ctx.send(embed=embed, view=view)

        except Exception as e:
            logger.error(f"Error getting playlists for user {ctx.author.id}: {e}")
            await ctx.fail("An error occurred while fetching your playlists. Please try again later.")


class SpotifyPlaylistView(discord.ui.View):
    """View for playing Spotify playlists"""

    def __init__(self, ctx, playlists):
        super().__init__(timeout=300)
        self.ctx = ctx
        self.playlists = playlists

        # Add buttons for each playlist (max 5 due to Discord limits)
        for i, playlist in enumerate(playlists[:5]):
            button = discord.ui.Button(
                label=f"{playlist['name'][:20]}{'...' if len(playlist['name']) > 20 else ''}",
                style=discord.ButtonStyle.green,
                custom_id=f"playlist_{i}"
            )
            button.callback = self.create_playlist_callback(i)
            self.add_item(button)

    def create_playlist_callback(self, index):
        async def playlist_callback(interaction: discord.Interaction):
            if interaction.user != self.ctx.author:
                await interaction.response.send_message("Only the command user can use these buttons.", ephemeral=True)
                return

            if not interaction.user.voice:
                await interaction.response.send_message("You need to be in a voice channel to play music.", ephemeral=True)
                return

            playlist = self.playlists[index]
            playlist_uri = f"spotify:playlist:{playlist['id']}"

            # Get the music cog and play the playlist
            music_cog = self.ctx.bot.get_cog("Music")
            if not music_cog:
                await interaction.response.send_message("Music functionality is not available.", ephemeral=True)
                return

            await interaction.response.defer()

            # Create a new context for the music command
            voice_channel = interaction.user.voice.channel
            vc = self.ctx.voice_client or await voice_channel.connect(cls=wavelink.Player)
            vc.ctx = self.ctx

            # Handle the Spotify playlist
            await music_cog.handle_spotify_playlist(self.ctx, vc, playlist_uri)

        return playlist_callback



